import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';
import styles from './QuillEditor.module.css';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

function QuillEditor(props: any) {
  const [value, setValue] = useState(props.value || '');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const editorRef = useRef<any>(null);

  useEffect(() => {
    let fullscreenBtn: HTMLButtonElement | null = null;
    let observer: MutationObserver | null = null;
    let timer: NodeJS.Timeout | null = null;

    // 创建全屏按钮的函数
    const createFullscreenButton = () => {
      const toolbar = document.querySelector('.ql-toolbar');
      if (toolbar && !toolbar.querySelector('.ql-fullscreen')) {
        // 创建全屏按钮
        fullscreenBtn = document.createElement('button');
        fullscreenBtn.className = `ql-fullscreen ${styles.fullscreenButton}`;
        fullscreenBtn.type = 'button';
        fullscreenBtn.title = '全屏';

        // 创建图标容器
        const iconContainer = document.createElement('span');
        iconContainer.className = styles.iconContainer;

        // 设置初始图标
        iconContainer.innerHTML =
          '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fullscreen-icon lucide-fullscreen"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><rect width="10" height="8" x="7" y="8" rx="1"/></svg>';

        fullscreenBtn.appendChild(iconContainer);

        // 添加点击事件 - 使用当前状态的引用
        const handleClick = (e: Event) => {
          e.preventDefault();
          e.stopPropagation();
          setIsFullscreen((prev) => !prev);
        };

        fullscreenBtn.addEventListener('click', handleClick);

        // 找到工具栏中最后一个按钮组，将全屏按钮插入到最后
        const lastButtonGroup = toolbar.querySelector('.ql-formats:last-child');
        if (lastButtonGroup) {
          lastButtonGroup.appendChild(fullscreenBtn);
        } else {
          toolbar.appendChild(fullscreenBtn);
        }

        console.log('全屏按钮已添加到工具栏');
        return fullscreenBtn;
      }
      return null;
    };

    // 更新按钮状态的函数
    const updateButtonState = () => {
      const btn = document.querySelector('.ql-fullscreen') as HTMLButtonElement;
      if (btn) {
        btn.title = isFullscreen ? '退出全屏' : '全屏';
        const iconContainer = btn.querySelector(`.${styles.iconContainer}`);
        if (iconContainer) {
          iconContainer.innerHTML = isFullscreen
            ? '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minimize-icon lucide-minimize"><path d="M8 3v3a2 2 0 0 1-2 2H3"/><path d="M21 8h-3a2 2 0 0 1-2-2V3"/><path d="M3 16h3a2 2 0 0 1 2 2v3"/><path d="M16 21v-3a2 2 0 0 1 2-2h3"/>'
            : '</svg><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-fullscreen-icon lucide-fullscreen"><path d="M3 7V5a2 2 0 0 1 2-2h2"/><path d="M17 3h2a2 2 0 0 1 2 2v2"/><path d="M21 17v2a2 2 0 0 1-2 2h-2"/><path d="M7 21H5a2 2 0 0 1-2-2v-2"/><rect width="10" height="8" x="7" y="8" rx="1"/></svg>';
        }
      }
    };

    // ESC键处理
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    // 管理页面滚动
    if (isFullscreen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // 更新按钮状态
    updateButtonState();

    // 如果是首次渲染，创建按钮
    if (!document.querySelector('.ql-fullscreen')) {
      // 使用MutationObserver监听DOM变化
      observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            const toolbar = document.querySelector('.ql-toolbar');
            if (toolbar && !toolbar.querySelector('.ql-fullscreen')) {
              createFullscreenButton();
            }
          }
        });
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      // 延迟添加按钮
      timer = setTimeout(() => {
        createFullscreenButton();
      }, 500);

      // 立即尝试添加
      createFullscreenButton();
    }

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';

      if (timer) clearTimeout(timer);
      if (observer) observer.disconnect();

      const btn = document.querySelector('.ql-fullscreen');
      if (btn && btn.parentNode) {
        btn.parentNode.removeChild(btn);
      }
    };
  }, [isFullscreen]);

  // 重点：handler 用 function，不要用箭头函数
  const modulesDefault = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, false] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [
          { list: 'ordered' },
          { list: 'bullet' },
          { indent: '-1' },
          { indent: '+1' },
        ],
        ['link', 'image'],
        ['clean'],
      ],
      // TODO 图片上传, 插入图片后输入文字导致图片重载，页面闪动。 复制粘贴图片上传cloudinary 还没处理
      // handlers: {
      //   image: async function imageHandler(this: any) {
      //     console.log('this', this);
      //     const input = document.createElement('input');
      //     input.setAttribute('type', 'file');
      //     input.setAttribute('accept', 'image/*');
      //     input.click();

      //     input.onchange = async () => {
      //       const file = input.files?.[0];
      //       if (file) {
      //         const { uploadImgToCloud } = await import('@/lib/cloudinary');

      //         let hideLoading: any;
      //         try {
      //           hideLoading = message.loading('图片上传中...', 0);
      //           const result = await uploadImgToCloud(file);
      //           if (result && result.secure_url) {
      //             const range = this.quill.getSelection();
      //             this.quill.insertEmbed(
      //               range.index,
      //               'image',
      //               result.secure_url
      //             );
      //             this.quill.setSelection(range.index + 1);
      //             hideLoading();

      //             message.success('图片上传成功');
      //           } else {
      //             hideLoading();
      //             message.error('图片上传失败，请重试');
      //           }
      //         } catch (error) {
      //           if (hideLoading) hideLoading();

      //           message.error('图片上传失败，请检查网络连接');
      //         }
      //       }
      //     };
      //   },
      // },
    },
  };

  function handleChange(newValue: string) {
    setValue(newValue);
    props.onChange(newValue);
  }

  return (
    <div
      className={`${styles.editorContainer} ${isFullscreen ? styles.fullscreenContainer : ''}`}
    >
      <ReactQuill
        {...props}
        ref={editorRef}
        value={value}
        onChange={handleChange}
        modules={props.modules || modulesDefault}
        className={isFullscreen ? styles.fullscreenEditor : ''}
      />
    </div>
  );
}

export default QuillEditor;
