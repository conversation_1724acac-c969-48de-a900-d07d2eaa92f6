import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';
import styles from './QuillEditor.module.css';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

function QuillEditor(props: any) {
  const [value, setValue] = useState(props.value || '');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const editorRef = useRef<any>(null);

  // 全屏切换功能
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理ESC键退出全屏
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  // 添加全屏按钮到现有工具栏
  useEffect(() => {
    const addFullscreenButton = () => {
      const toolbar = document.querySelector('.ql-toolbar');
      if (toolbar && !toolbar.querySelector('.ql-fullscreen')) {
        // 创建全屏按钮
        const fullscreenBtn = document.createElement('button');
        fullscreenBtn.className = `ql-fullscreen ${styles.fullscreenButton}`;
        fullscreenBtn.type = 'button';
        fullscreenBtn.title = '全屏';

        // 创建图标容器
        const iconContainer = document.createElement('span');
        iconContainer.className = styles.iconContainer;

        // 设置初始图标 - 四角都是箭头的全屏图标
        iconContainer.innerHTML =
          '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shrink-icon lucide-shrink"><path d="m15 15 6 6m-6-6v4.8m0-4.8h4.8"/><path d="M9 19.8V15m0 0H4.2M9 15l-6 6"/><path d="M15 4.2V9m0 0h4.8M15 9l6-6"/><path d="M9 4.2V9m0 0H4.2M9 9 3 3"/></svg>';

        fullscreenBtn.appendChild(iconContainer);

        // 添加点击事件 - 使用箭头函数确保this绑定正确
        fullscreenBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          toggleFullscreen();
        });

        // 找到工具栏中最后一个按钮组，将全屏按钮插入到最后
        const lastButtonGroup = toolbar.querySelector('.ql-formats:last-child');
        if (lastButtonGroup) {
          lastButtonGroup.appendChild(fullscreenBtn);
        } else {
          // 如果没有找到按钮组，就添加到工具栏末尾
          toolbar.appendChild(fullscreenBtn);
        }

        console.log('全屏按钮已添加到工具栏', fullscreenBtn);
        return fullscreenBtn;
      }
      return null;
    };

    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          const toolbar = document.querySelector('.ql-toolbar');
          if (toolbar && !toolbar.querySelector('.ql-fullscreen')) {
            addFullscreenButton();
          }
        }
      });
    });

    // 开始观察
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // 延迟添加按钮，确保工具栏已经渲染
    const timer = setTimeout(() => {
      addFullscreenButton();
    }, 500);

    // 也尝试立即添加
    addFullscreenButton();

    return () => {
      clearTimeout(timer);
      observer.disconnect();
      const fullscreenBtn = document.querySelector('.ql-fullscreen');
      if (fullscreenBtn && fullscreenBtn.parentNode) {
        fullscreenBtn.parentNode.removeChild(fullscreenBtn);
      }
    };
  }, []);

  // 更新按钮图标和标题
  useEffect(() => {
    const fullscreenBtn = document.querySelector(
      '.ql-fullscreen'
    ) as HTMLButtonElement;
    if (fullscreenBtn) {
      fullscreenBtn.title = isFullscreen ? '退出全屏' : '全屏';
      const iconContainer = fullscreenBtn.querySelector(
        `.${styles.iconContainer}`
      );
      if (iconContainer) {
        // 更新图标但不重新绑定事件
        iconContainer.innerHTML = isFullscreen
          ? '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-expand-icon lucide-expand"><path d="m15 15 6 6"/><path d="m15 9 6-6"/><path d="M21 16v5h-5"/><path d="M21 8V3h-5"/><path d="M3 16v5h5"/><path d="m3 21 6-6"/><path d="M3 8V3h5"/><path d="M9 9 3 3"/></svg>'
          : '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-shrink-icon lucide-shrink"><path d="m15 15 6 6m-6-6v4.8m0-4.8h4.8"/><path d="M9 19.8V15m0 0H4.2M9 15l-6 6"/><path d="M15 4.2V9m0 0h4.8M15 9l6-6"/><path d="M9 4.2V9m0 0H4.2M9 9 3 3"/></svg>';
      }

      // 确保点击事件仍然有效 - 重新绑定事件
      fullscreenBtn.onclick = (e) => {
        e.preventDefault();
        e.stopPropagation();
        toggleFullscreen();
      };
    }
  }, [isFullscreen]);

  // 重点：handler 用 function，不要用箭头函数
  const modulesDefault = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, false] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [
          { list: 'ordered' },
          { list: 'bullet' },
          { indent: '-1' },
          { indent: '+1' },
        ],
        ['link', 'image'],
        ['clean'],
      ],
      // TODO 图片上传, 插入图片后输入文字导致图片重载，页面闪动。 复制粘贴图片上传cloudinary 还没处理
      // handlers: {
      //   image: async function imageHandler(this: any) {
      //     console.log('this', this);
      //     const input = document.createElement('input');
      //     input.setAttribute('type', 'file');
      //     input.setAttribute('accept', 'image/*');
      //     input.click();

      //     input.onchange = async () => {
      //       const file = input.files?.[0];
      //       if (file) {
      //         const { uploadImgToCloud } = await import('@/lib/cloudinary');

      //         let hideLoading: any;
      //         try {
      //           hideLoading = message.loading('图片上传中...', 0);
      //           const result = await uploadImgToCloud(file);
      //           if (result && result.secure_url) {
      //             const range = this.quill.getSelection();
      //             this.quill.insertEmbed(
      //               range.index,
      //               'image',
      //               result.secure_url
      //             );
      //             this.quill.setSelection(range.index + 1);
      //             hideLoading();

      //             message.success('图片上传成功');
      //           } else {
      //             hideLoading();
      //             message.error('图片上传失败，请重试');
      //           }
      //         } catch (error) {
      //           if (hideLoading) hideLoading();

      //           message.error('图片上传失败，请检查网络连接');
      //         }
      //       }
      //     };
      //   },
      // },
    },
  };

  function handleChange(newValue: string) {
    setValue(newValue);
    props.onChange(newValue);
  }

  return (
    <div
      className={`${styles.editorContainer} ${isFullscreen ? styles.fullscreenContainer : ''}`}
    >
      <ReactQuill
        {...props}
        ref={editorRef}
        value={value}
        onChange={handleChange}
        modules={props.modules || modulesDefault}
        className={isFullscreen ? styles.fullscreenEditor : ''}
      />
    </div>
  );
}

export default QuillEditor;
