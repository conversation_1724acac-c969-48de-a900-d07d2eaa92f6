import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill-new/dist/quill.snow.css';

const ReactQuill = dynamic(() => import('react-quill-new'), { ssr: false });

function QuillEditor(props: any) {
  const [value, setValue] = useState(props.value || '');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const editorRef = useRef<any>(null);

  // 全屏切换功能
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // 处理ESC键退出全屏
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  // 添加全屏按钮到现有工具栏
  useEffect(() => {
    const toolbar = document.querySelector('.ql-toolbar');
    if (toolbar && !toolbar.querySelector('.ql-fullscreen')) {
      // 创建全屏按钮
      const fullscreenBtn = document.createElement('button');
      fullscreenBtn.className = 'ql-fullscreen';
      fullscreenBtn.type = 'button';
      fullscreenBtn.title = isFullscreen ? '退出全屏' : '全屏';

      // 设置按钮样式
      Object.assign(fullscreenBtn.style, {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '28px',
        height: '28px',
        border: 'none',
        background: 'transparent',
        cursor: 'pointer',
        borderRadius: '4px',
        transition: 'all 0.2s ease',
        color: '#666',
        marginLeft: '4px',
      });

      // 创建图标容器
      const iconContainer = document.createElement('span');
      iconContainer.className = 'fullscreen-icon';
      Object.assign(iconContainer.style, {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      });
      fullscreenBtn.appendChild(iconContainer);

      // 添加悬停效果
      fullscreenBtn.addEventListener('mouseenter', () => {
        fullscreenBtn.style.background = '#f0f0f0';
        fullscreenBtn.style.color = '#333';
      });
      fullscreenBtn.addEventListener('mouseleave', () => {
        fullscreenBtn.style.background = 'transparent';
        fullscreenBtn.style.color = '#666';
      });

      // 添加点击事件
      fullscreenBtn.addEventListener('click', toggleFullscreen);

      // 添加到工具栏末尾
      toolbar.appendChild(fullscreenBtn);

      // 清理函数
      return () => {
        if (fullscreenBtn.parentNode) {
          fullscreenBtn.parentNode.removeChild(fullscreenBtn);
        }
      };
    }
  }, []);

  // 更新按钮图标和标题
  useEffect(() => {
    const fullscreenBtn = document.querySelector(
      '.ql-fullscreen'
    ) as HTMLButtonElement;
    if (fullscreenBtn) {
      fullscreenBtn.title = isFullscreen ? '退出全屏' : '全屏';
      const iconContainer = fullscreenBtn.querySelector('.fullscreen-icon');
      if (iconContainer) {
        iconContainer.innerHTML = isFullscreen
          ? '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3v3a2 2 0 0 1-2 2H3"/><path d="M21 8h-3a2 2 0 0 1-2-2V3"/><path d="M3 16h3a2 2 0 0 1 2 2v3"/><path d="M16 21v-3a2 2 0 0 1 2-2h3"/></svg>'
          : '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M15 3h6v6"/><path d="M9 21H3v-6"/><path d="M21 3l-7 7"/><path d="M3 21l7-7"/></svg>';
      }
    }
  }, [isFullscreen]);

  // 添加全屏模式的CSS样式
  useEffect(() => {
    const styleId = 'quill-fullscreen-styles';
    if (isFullscreen && !document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .ql-container.ql-snow {
          flex: 1 !important;
          font-size: 16px !important;
        }
        .ql-editor {
          min-height: auto !important;
          height: 100% !important;
          padding: 20px !important;
        }
        .ql-toolbar.ql-snow {
          background: #fafafa !important;
          border-bottom: 1px solid #e0e0e0 !important;
          padding: 12px 20px !important;
        }
        @media (max-width: 768px) {
          .ql-editor {
            padding: 15px !important;
            font-size: 14px !important;
          }
          .ql-toolbar.ql-snow {
            padding: 10px 15px !important;
          }
        }
      `;
      document.head.appendChild(style);
    } else if (!isFullscreen) {
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }
    }

    return () => {
      const existingStyle = document.getElementById(styleId);
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, [isFullscreen]);

  // 重点：handler 用 function，不要用箭头函数
  const modulesDefault = {
    toolbar: {
      container: [
        [{ header: [1, 2, 3, 4, 5, false] }],
        ['bold', 'italic', 'underline', 'strike', 'blockquote'],
        [
          { list: 'ordered' },
          { list: 'bullet' },
          { indent: '-1' },
          { indent: '+1' },
        ],
        ['link', 'image'],
        ['clean'],
      ],
      // TODO 图片上传, 插入图片后输入文字导致图片重载，页面闪动。 复制粘贴图片上传cloudinary 还没处理
      // handlers: {
      //   image: async function imageHandler(this: any) {
      //     console.log('this', this);
      //     const input = document.createElement('input');
      //     input.setAttribute('type', 'file');
      //     input.setAttribute('accept', 'image/*');
      //     input.click();

      //     input.onchange = async () => {
      //       const file = input.files?.[0];
      //       if (file) {
      //         const { uploadImgToCloud } = await import('@/lib/cloudinary');

      //         let hideLoading: any;
      //         try {
      //           hideLoading = message.loading('图片上传中...', 0);
      //           const result = await uploadImgToCloud(file);
      //           if (result && result.secure_url) {
      //             const range = this.quill.getSelection();
      //             this.quill.insertEmbed(
      //               range.index,
      //               'image',
      //               result.secure_url
      //             );
      //             this.quill.setSelection(range.index + 1);
      //             hideLoading();

      //             message.success('图片上传成功');
      //           } else {
      //             hideLoading();
      //             message.error('图片上传失败，请重试');
      //           }
      //         } catch (error) {
      //           if (hideLoading) hideLoading();

      //           message.error('图片上传失败，请检查网络连接');
      //         }
      //       }
      //     };
      //   },
      // },
    },
  };

  function handleChange(newValue: string) {
    setValue(newValue);
    props.onChange(newValue);
  }

  return (
    <div
      style={{
        position: isFullscreen ? 'fixed' : 'relative',
        top: isFullscreen ? 0 : 'auto',
        left: isFullscreen ? 0 : 'auto',
        right: isFullscreen ? 0 : 'auto',
        bottom: isFullscreen ? 0 : 'auto',
        zIndex: isFullscreen ? 9999 : 'auto',
        background: isFullscreen ? 'white' : 'transparent',
        display: isFullscreen ? 'flex' : 'block',
        flexDirection: isFullscreen ? 'column' : 'row',
        transition: 'all 0.3s ease',
      }}
    >
      <ReactQuill
        {...props}
        ref={editorRef}
        value={value}
        onChange={handleChange}
        modules={props.modules || modulesDefault}
        style={
          isFullscreen
            ? {
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
              }
            : {}
        }
      />
    </div>
  );
}

export default QuillEditor;
