import React, { useState } from 'react';
import { Card, Button, Space } from 'antd';
import QuillEditor from '@/components/quillEditor/QuillEditor';

export default function TestFullscreen() {
  const [content, setContent] = useState('<p>测试全屏功能！请查看工具栏右侧是否有全屏按钮。</p>');

  const handleContentChange = (value: string) => {
    setContent(value);
  };

  const handleClear = () => {
    setContent('');
  };

  const handleSetSample = () => {
    setContent(`
      <h1>全屏富文本编辑器测试</h1>
      <p>这是一个支持全屏功能的富文本编辑器。</p>
      <h2>主要功能：</h2>
      <ul>
        <li><strong>全屏编辑</strong>：点击工具栏右侧的全屏图标</li>
        <li><strong>ESC退出</strong>：在全屏模式下按ESC键退出</li>
        <li><strong>响应式设计</strong>：支持移动端和桌面端</li>
        <li><strong>丰富的格式</strong>：支持标题、列表、链接等</li>
      </ul>
      <blockquote>
        <p>在全屏模式下，您可以专注于内容创作，享受更好的写作体验。</p>
      </blockquote>
      <p>试试看吧！</p>
    `);
  };

  return (
    <div style={{ 
      maxWidth: '1200px', 
      margin: '0 auto', 
      padding: '20px',
      minHeight: '100vh',
      background: '#f5f5f5'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        padding: '20px',
        background: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
      }}>
        <h1 style={{ margin: 0, color: '#333', fontSize: '24px' }}>
          QuillEditor 全屏功能测试
        </h1>
        <Space>
          <Button onClick={handleSetSample}>加载示例内容</Button>
          <Button onClick={handleClear}>清空内容</Button>
        </Space>
      </div>
      
      <Card 
        title="富文本编辑器 - 请查看工具栏右侧的全屏按钮"
        style={{ 
          marginBottom: '20px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        <QuillEditor
          value={content}
          onChange={handleContentChange}
          placeholder="开始输入内容，或点击右上角的全屏按钮进入全屏模式..."
        />
      </Card>
      
      <Card 
        title="内容预览"
        style={{ 
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        <div 
          style={{
            minHeight: '200px',
            padding: '16px',
            background: '#fafafa',
            borderRadius: '6px',
            border: '1px solid #e0e0e0'
          }}
          dangerouslySetInnerHTML={{ __html: content }}
        />
      </Card>
    </div>
  );
}
